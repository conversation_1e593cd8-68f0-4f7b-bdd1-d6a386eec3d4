'use client';

import { KEYWORDS_LOADING_KEYS, LOADING_SCOPES } from '@/constants';
import { KeywordWithDetail } from '@/models';
import { keywordStorage } from '@/lib/keyword-storage';
import { useKeywordSync } from '@/hooks/use-keyword-sync';
import {
	createContext,
	useCallback,
	useContext,
	useEffect,
	useMemo,
	useRef,
	useState,
} from 'react';
import { useLoadingError, useScopedLoading } from './loading-context';

type KeywordsContextType = {
	keywords: KeywordWithDetail[];
	isLoading: boolean;
	error: Error | null;
	selectedKeywords: string[];
	setSelectedKeywords: (ids: string[]) => void;
	fetchKeywords: () => Promise<void>;
	getKeyword: (id: string) => Promise<KeywordWithDetail | null>;
	searchKeywords: (term: string) => Promise<void>;
	createKeyword: (name: string) => Promise<KeywordWithDetail | null>;
	updateKeyword: (id: string, name: string) => Promise<KeywordWithDetail | null>;
	deleteKeyword: (id: string) => Promise<void>;
	getLoadingState: (key: string) => boolean;
	syncStatus: {
		pendingActions: number;
		isSync: boolean;
		hasUnsyncedChanges: boolean;
		showSyncSuccess: boolean;
	};
	syncNow: () => Promise<void>;
};

const KeywordsContext = createContext<KeywordsContextType | undefined>(undefined);

export function KeywordsProvider({ children }: { children: React.ReactNode }) {
	const [keywords, setKeywords] = useState<KeywordWithDetail[]>([]);
	const [error, setError] = useState<Error | null>(null);
	const [selectedKeywords, setSelectedKeywords] = useState<string[]>([]);
	const [syncStatus, setSyncStatus] = useState({
		pendingActions: 0,
		isSync: false,
		hasUnsyncedChanges: false,
		showSyncSuccess: false,
	});
	const { getLoading } = useScopedLoading(LOADING_SCOPES.KEYWORDS);
	const loadingErrorHelper = useLoadingError(LOADING_SCOPES.KEYWORDS);

	// Initialize background sync
	const { syncNow, fetchAndUpdateKeywords, getSyncStatus } = useKeywordSync({
		onSyncSuccess: (action) => {
			console.log('Keyword sync success:', action);
		},
		onSyncError: (action, error) => {
			console.error('Keyword sync error:', action, error);
			setError(error);
		},
	});

	// Load keywords from IndexedDB on mount
	useEffect(() => {
		const loadKeywords = async () => {
			try {
				const localKeywords = await keywordStorage.getKeywords();
				const localSelectedKeywords = await keywordStorage.getSelectedKeywords();
				setKeywords(localKeywords);
				setSelectedKeywords(localSelectedKeywords);
			} catch (error) {
				console.error('Failed to load keywords from IndexedDB:', error);
				setError(error instanceof Error ? error : new Error('Failed to load keywords'));
			}
		};
		loadKeywords();
	}, []);

	// Computed loading state - only show loading for initial fetch
	const isLoading = getLoading(KEYWORDS_LOADING_KEYS.FETCH_KEYWORDS);

	const fetchKeywords = useCallback(async () => {
		const { start, end } = loadingErrorHelper(
			() => {},
			setError,
			KEYWORDS_LOADING_KEYS.FETCH_KEYWORDS
		);
		start();
		try {
			const result = await fetchAndUpdateKeywords();
			setKeywords(result);
			end();
		} catch (err) {
			const error = err instanceof Error ? err : new Error('Failed to fetch keywords');
			end(error);
		}
	}, [loadingErrorHelper, fetchAndUpdateKeywords]);

	const getKeyword = useCallback(async (id: string): Promise<KeywordWithDetail | null> => {
		// First try to get from IndexedDB
		const localKeywords = await keywordStorage.getKeywords();
		const localKeyword = localKeywords.find((k) => k.id === id);
		if (localKeyword) {
			return localKeyword;
		}

		// If not found locally, fetch from server
		try {
			const response = await fetch(`/api/keywords/${id}`);
			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to get keyword');
			}
			const result = await response.json();
			return result;
		} catch (err) {
			console.error('Failed to fetch keyword from server:', err);
			return null;
		}
	}, []);

	const searchKeywords = useCallback(async (term: string) => {
		// Search in IndexedDB
		const localKeywords = await keywordStorage.getKeywords();
		const filtered = localKeywords.filter((keyword) =>
			keyword.content.toLowerCase().includes(term.toLowerCase())
		);
		setKeywords(filtered);
	}, []);

	const createKeyword = useCallback(async (name: string) => {
		try {
			// Create temporary keyword for optimistic update
			const tempKeyword: KeywordWithDetail = {
				id: keywordStorage.generateActionId(),
				content: name.trim(),
				user_id: 'temp', // Will be updated when synced
			};

			// Add to IndexedDB immediately (optimistic update)
			await keywordStorage.addKeywordLocally(tempKeyword);
			const updatedKeywords = await keywordStorage.getKeywords();
			setKeywords(updatedKeywords);

			// Add to sync queue for background processing
			await keywordStorage.addToSyncQueue({
				id: tempKeyword.id,
				type: 'create',
				data: { name: name.trim() },
			});

			return tempKeyword;
		} catch (err) {
			const error = err instanceof Error ? err : new Error('Failed to create keyword');
			setError(error);
			return null;
		}
	}, []);

	const updateKeyword = useCallback(async (id: string, name: string) => {
		try {
			// Update locally immediately (optimistic update)
			await keywordStorage.updateKeywordLocally(id, {
				content: name.trim(),
			});
			const updatedKeywords = await keywordStorage.getKeywords();
			setKeywords(updatedKeywords);

			// Add to sync queue for background processing
			await keywordStorage.addToSyncQueue({
				id: keywordStorage.generateActionId(),
				type: 'update',
				data: { keywordId: id, name: name.trim() },
			});

			// Return the updated keyword
			return updatedKeywords.find((k) => k.id === id) || null;
		} catch (err) {
			const error = err instanceof Error ? err : new Error('Failed to update keyword');
			setError(error);
			return null;
		}
	}, []);

	const deleteKeyword = useCallback(async (id: string) => {
		try {
			// Clean up any existing sync actions for this keyword to prevent conflicts
			await keywordStorage.cleanupConflictingActions(id);

			// Delete locally immediately (optimistic update)
			await keywordStorage.deleteKeywordLocally(id);
			const updatedKeywords = await keywordStorage.getKeywords();
			const updatedSelected = await keywordStorage.getSelectedKeywords();
			setKeywords(updatedKeywords);
			setSelectedKeywords(updatedSelected);

			// Check if this keyword has a real server ID (not a temporary ID)
			// Temporary IDs start with "action_" prefix
			const isTemporaryKeyword = id.startsWith('action_');

			if (!isTemporaryKeyword) {
				// This is a real keyword from the server, add delete action to sync queue
				await keywordStorage.addToSyncQueue({
					id: keywordStorage.generateActionId(),
					type: 'delete',
					data: { keywordId: id },
				});
			} else {
				console.log(`Deleted temporary keyword ${id} without server sync`);
			}
		} catch (err) {
			const error = err instanceof Error ? err : new Error('Failed to delete keyword');
			setError(error);
		}
	}, []);

	// Custom setSelectedKeywords that also saves to IndexedDB
	const setSelectedKeywordsWithStorage = useCallback(async (ids: string[]) => {
		setSelectedKeywords(ids);
		await keywordStorage.saveSelectedKeywords(ids);
	}, []);

	const isFirstLoad = useRef(true);
	useEffect(() => {
		const checkAndFetch = async () => {
			if (isFirstLoad.current) {
				isFirstLoad.current = false;
				// Only fetch from server if no local data exists
				const localKeywords = await keywordStorage.getKeywords();
				if (localKeywords.length === 0) {
					fetchKeywords();
				}
			}
		};
		checkAndFetch();
	}, [fetchKeywords]);

	// Update sync status periodically
	useEffect(() => {
		const updateSyncStatus = async () => {
			const status = await getSyncStatus();
			setSyncStatus(status);
		};

		// Initial update
		updateSyncStatus();

		// Update every 2 seconds
		const interval = setInterval(updateSyncStatus, 2000);

		return () => clearInterval(interval);
	}, [getSyncStatus]);

	const value = useMemo(
		() => ({
			keywords,
			isLoading,
			error,
			selectedKeywords,
			setSelectedKeywords: setSelectedKeywordsWithStorage,
			fetchKeywords,
			getKeyword,
			searchKeywords,
			createKeyword,
			updateKeyword,
			deleteKeyword,
			getLoadingState: getLoading,
			syncStatus,
			syncNow,
		}),
		[
			keywords,
			isLoading,
			error,
			selectedKeywords,
			setSelectedKeywordsWithStorage,
			fetchKeywords,
			getKeyword,
			searchKeywords,
			createKeyword,
			updateKeyword,
			deleteKeyword,
			getLoading,
			syncStatus,
			syncNow,
		]
	);

	return <KeywordsContext.Provider value={value}>{children}</KeywordsContext.Provider>;
}

export function useKeywordsContext() {
	const context = useContext(KeywordsContext);
	if (context === undefined)
		throw new Error('useKeywordsContext must be used within a KeywordsProvider');
	return context;
}
