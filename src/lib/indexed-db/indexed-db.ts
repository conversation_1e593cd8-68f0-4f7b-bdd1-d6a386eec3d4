export const DB_NAME = 'vocab-db';
export const DB_VERSION = 3; // Incremented to fix object store creation issue

// Centralized store configurations for all features
const ALL_STORE_CONFIGS = [
	// Collection stores
	{ storeName: 'collections', options: { keyPath: 'id' } },
	// Keyword stores
	{ storeName: 'keywords', options: { keyPath: 'id' } },
	{ storeName: 'selected-keywords', options: { keyPath: 'id' } },
	{ storeName: 'keyword-sync-queue', options: { keyPath: 'id' } },
];

/**
 * A generic manager for interacting with IndexedDB.
 */
export class IndexedDBManager {
	private dbName: string;
	private dbVersion: number;
	private storeConfigs: { storeName: string; options?: IDBObjectStoreParameters }[];
	private db: IDBDatabase | null = null;

	constructor(
		dbName: string,
		dbVersion: number,
		storeConfigs: { storeName: string; options?: IDBObjectStoreParameters }[]
	) {
		this.dbName = dbName;
		this.dbVersion = dbVersion;
		this.storeConfigs = storeConfigs;
	}

	/**
	 * Opens the IndexedDB database and creates object stores if necessary.
	 * @returns A Promise that resolves with the database instance.
	 */
	private async openDB(): Promise<IDBDatabase> {
		if (this.db) {
			return Promise.resolve(this.db);
		}

		return new Promise((resolve, reject) => {
			const request = indexedDB.open(this.dbName, this.dbVersion);

			request.onupgradeneeded = (event) => {
				const db = (event.target as IDBOpenDBRequest).result;
				this.storeConfigs.forEach((config) => {
					if (!db.objectStoreNames.contains(config.storeName)) {
						db.createObjectStore(config.storeName, config.options);
					}
				});
			};

			request.onsuccess = (event) => {
				this.db = (event.target as IDBOpenDBRequest).result;
				resolve(this.db!);
			};

			request.onerror = (event) => {
				reject((event.target as IDBOpenDBRequest).error);
			};
		});
	}

	/**
	 * Gets the IndexedDB database instance, opening it if necessary.
	 * @returns A Promise that resolves with the database instance.
	 */
	public async getDB(): Promise<IDBDatabase> {
		return this.openDB();
	}

	/**
	 * Puts (adds or updates) data into a specific object store.
	 * @param storeName The name of the object store.
	 * @param data The data object to put.
	 */
	public async put<T>(storeName: string, data: T): Promise<void> {
		const db = await this.openDB();
		return new Promise((resolve, reject) => {
			const transaction = db.transaction([storeName], 'readwrite');
			const store = transaction.objectStore(storeName);
			const request = store.put(data);

			request.onsuccess = () => resolve();
			request.onerror = (event) => reject((event.target as IDBRequest).error);
		});
	}

	/**
	 * Gets data from a specific object store by key.
	 * @param storeName The name of the object store.
	 * @param key The key of the data to retrieve.
	 * @returns A Promise that resolves with the data object, or null if not found.
	 */
	public async get<T>(storeName: string, key: IDBValidKey): Promise<T | null> {
		const db = await this.openDB();
		return new Promise((resolve, reject) => {
			const transaction = db.transaction([storeName], 'readonly');
			const store = transaction.objectStore(storeName);
			const request = store.get(key);

			request.onsuccess = (event) => {
				resolve((event.target as IDBRequest).result || null);
			};
			request.onerror = (event) => reject((event.target as IDBRequest).error);
		});
	}

	/**
	 * Gets all data from a specific object store.
	 * @param storeName The name of the object store.
	 * @returns A Promise that resolves with an array of all data objects.
	 */
	public async getAll<T>(storeName: string): Promise<T[]> {
		const db = await this.openDB();
		return new Promise((resolve, reject) => {
			const transaction = db.transaction([storeName], 'readonly');
			const store = transaction.objectStore(storeName);
			const request = store.getAll();

			request.onsuccess = (event) => {
				resolve((event.target as IDBRequest).result as T[]);
			};
			request.onerror = (event) => reject((event.target as IDBRequest).error);
		});
	}

	/**
	 * Deletes data from a specific object store by key.
	 * @param storeName The name of the object store.
	 * @param key The key of the data to delete.
	 */
	public async delete(storeName: string, key: IDBValidKey): Promise<void> {
		const db = await this.openDB();
		return new Promise((resolve, reject) => {
			const transaction = db.transaction([storeName], 'readwrite');
			const store = transaction.objectStore(storeName);
			const request = store.delete(key);

			request.onsuccess = () => resolve();
			request.onerror = (event) => reject((event.target as IDBRequest).error);
		});
	}

	/**
	 * Clears all data from a specific object store.
	 * @param storeName The name of the object store.
	 */
	public async clear(storeName: string): Promise<void> {
		const db = await this.openDB();
		return new Promise((resolve, reject) => {
			const transaction = db.transaction([storeName], 'readwrite');
			const store = transaction.objectStore(storeName);
			const request = store.clear();

			request.onsuccess = () => resolve();
			request.onerror = (event) => reject((event.target as IDBRequest).error);
		});
	}
}

// Singleton instance to ensure all parts of the app use the same database manager
let sharedDBManager: IndexedDBManager | null = null;

/**
 * Get the shared IndexedDB manager instance with all store configurations
 */
export function getSharedDBManager(): IndexedDBManager {
	if (!sharedDBManager) {
		sharedDBManager = new IndexedDBManager(DB_NAME, DB_VERSION, ALL_STORE_CONFIGS);
	}
	return sharedDBManager;
}

/**
 * Reset the shared IndexedDB manager (useful for testing or error recovery)
 */
export function resetSharedDBManager(): void {
	if (sharedDBManager) {
		sharedDBManager = null;
	}
}

/**
 * Clear all data from IndexedDB and reset the manager
 */
export async function clearAllIndexedDBData(): Promise<void> {
	try {
		const manager = getSharedDBManager();
		const db = await manager.getDB();

		// Clear all object stores
		for (const config of ALL_STORE_CONFIGS) {
			try {
				await manager.clear(config.storeName);
			} catch (error) {
				console.warn(`Failed to clear store ${config.storeName}:`, error);
			}
		}

		console.log('All IndexedDB data cleared successfully');
	} catch (error) {
		console.error('Failed to clear IndexedDB data:', error);
		// If clearing fails, try to delete the entire database
		try {
			await deleteIndexedDB();
		} catch (deleteError) {
			console.error('Failed to delete IndexedDB:', deleteError);
		}
	}
}

/**
 * Delete the entire IndexedDB database
 */
export async function deleteIndexedDB(): Promise<void> {
	return new Promise((resolve, reject) => {
		// Close any existing connections
		if (sharedDBManager) {
			sharedDBManager = null;
		}

		const deleteRequest = indexedDB.deleteDatabase(DB_NAME);

		deleteRequest.onsuccess = () => {
			console.log('IndexedDB database deleted successfully');
			resolve();
		};

		deleteRequest.onerror = (event) => {
			console.error(
				'Failed to delete IndexedDB database:',
				(event.target as IDBOpenDBRequest).error
			);
			reject((event.target as IDBOpenDBRequest).error);
		};

		deleteRequest.onblocked = () => {
			console.warn('IndexedDB deletion blocked - close all tabs and try again');
			// Still resolve as the deletion will complete when tabs are closed
			resolve();
		};
	});
}
