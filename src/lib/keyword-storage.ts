import { KeywordWithDetail } from '@/models';
import * as keywordIndexedDB from './indexed-db/keyword-indexed-db';
import { forceRecreateDatabase } from './indexed-db';

// Legacy localStorage keys for migration
const KEYWORDS_STORAGE_KEY = 'vocab-keywords';
const SELECTED_KEYWORDS_STORAGE_KEY = 'vocab-selected-keywords';
const KEYWORDS_SYNC_QUEUE_KEY = 'vocab-keywords-sync-queue';
const MIGRATION_COMPLETED_KEY = 'vocab-keywords-migration-completed';

export interface KeywordSyncAction {
	id: string;
	type: 'create' | 'update' | 'delete';
	data?: {
		name?: string;
		keywordId?: string;
	};
	timestamp: number;
}

export interface KeywordStorageData {
	keywords: KeywordWithDetail[];
	lastSyncTimestamp: number;
	version: number;
}

/**
 * Utility class for managing keywords in IndexedDB with background sync
 */
export class KeywordStorage {
	private static instance: KeywordStorage;
	private migrationPromise: Promise<void> | null = null;

	private constructor() {
		// Start migration on first instantiation
		this.migrationPromise = this.migrateFromLocalStorage();
	}

	static getInstance(): KeywordStorage {
		if (!KeywordStorage.instance) {
			KeywordStorage.instance = new KeywordStorage();
		}
		return KeywordStorage.instance;
	}

	/**
	 * Ensure migration is completed before any operation
	 */
	private async ensureMigration(): Promise<void> {
		if (this.migrationPromise) {
			await this.migrationPromise;
		}
	}

	/**
	 * Migrate data from localStorage to IndexedDB (one-time operation)
	 */
	private async migrateFromLocalStorage(): Promise<void> {
		try {
			// Skip migration on server-side (localStorage not available)
			if (typeof window === 'undefined') {
				return;
			}

			// Check if migration already completed
			const migrationCompleted = localStorage.getItem(MIGRATION_COMPLETED_KEY);
			if (migrationCompleted === 'true') {
				return;
			}

			console.log('Starting keyword migration from localStorage to IndexedDB...');

			// Migrate keywords
			const keywordsData = localStorage.getItem(KEYWORDS_STORAGE_KEY);
			if (keywordsData) {
				try {
					const parsed: KeywordStorageData = JSON.parse(keywordsData);
					if (parsed.keywords && parsed.keywords.length > 0) {
						await keywordIndexedDB.saveKeywords(parsed.keywords);
						console.log(`Migrated ${parsed.keywords.length} keywords to IndexedDB`);
					}
				} catch (error) {
					console.error('Failed to migrate keywords:', error);
				}
			}

			// Migrate selected keywords
			const selectedData = localStorage.getItem(SELECTED_KEYWORDS_STORAGE_KEY);
			if (selectedData) {
				try {
					const selectedIds: string[] = JSON.parse(selectedData);
					if (selectedIds.length > 0) {
						await keywordIndexedDB.saveSelectedKeywords(selectedIds);
						console.log(
							`Migrated ${selectedIds.length} selected keywords to IndexedDB`
						);
					}
				} catch (error) {
					console.error('Failed to migrate selected keywords:', error);
				}
			}

			// Migrate sync queue
			const syncQueueData = localStorage.getItem(KEYWORDS_SYNC_QUEUE_KEY);
			if (syncQueueData) {
				try {
					const syncQueue: KeywordSyncAction[] = JSON.parse(syncQueueData);
					for (const action of syncQueue) {
						await keywordIndexedDB.addToSyncQueue(action);
					}
					console.log(`Migrated ${syncQueue.length} sync actions to IndexedDB`);
				} catch (error) {
					console.error('Failed to migrate sync queue:', error);
				}
			}

			// Mark migration as completed
			localStorage.setItem(MIGRATION_COMPLETED_KEY, 'true');

			// Clean up old localStorage data
			localStorage.removeItem(KEYWORDS_STORAGE_KEY);
			localStorage.removeItem(SELECTED_KEYWORDS_STORAGE_KEY);
			localStorage.removeItem(KEYWORDS_SYNC_QUEUE_KEY);

			console.log('Keyword migration completed successfully');
		} catch (error) {
			console.error('Failed to migrate keywords from localStorage:', error);
		}
	}

	/**
	 * Get keywords from IndexedDB
	 */
	async getKeywords(): Promise<KeywordWithDetail[]> {
		await this.ensureMigration();
		return keywordIndexedDB.getKeywords();
	}

	/**
	 * Save keywords to IndexedDB
	 */
	async saveKeywords(keywords: KeywordWithDetail[]): Promise<void> {
		await this.ensureMigration();
		return keywordIndexedDB.saveKeywords(keywords);
	}

	/**
	 * Get selected keywords from IndexedDB
	 */
	async getSelectedKeywords(): Promise<string[]> {
		await this.ensureMigration();
		return keywordIndexedDB.getSelectedKeywords();
	}

	/**
	 * Save selected keywords to IndexedDB
	 */
	async saveSelectedKeywords(selectedIds: string[]): Promise<void> {
		await this.ensureMigration();
		return keywordIndexedDB.saveSelectedKeywords(selectedIds);
	}

	/**
	 * Add keyword locally (optimistic update)
	 */
	async addKeywordLocally(keyword: KeywordWithDetail): Promise<void> {
		await this.ensureMigration();
		const keywords = await this.getKeywords();
		const updatedKeywords = [...keywords, keyword];
		return this.saveKeywords(updatedKeywords);
	}

	/**
	 * Update keyword locally (optimistic update)
	 */
	async updateKeywordLocally(id: string, updates: Partial<KeywordWithDetail>): Promise<void> {
		await this.ensureMigration();
		const keywords = await this.getKeywords();
		const updatedKeywords = keywords.map((keyword) =>
			keyword.id === id ? { ...keyword, ...updates } : keyword
		);
		return this.saveKeywords(updatedKeywords);
	}

	/**
	 * Delete keyword locally (optimistic update)
	 */
	async deleteKeywordLocally(id: string): Promise<void> {
		await this.ensureMigration();
		const keywords = await this.getKeywords();
		const updatedKeywords = keywords.filter((keyword) => keyword.id !== id);
		await this.saveKeywords(updatedKeywords);

		// Also remove from selected keywords
		const selectedKeywords = await this.getSelectedKeywords();
		const updatedSelected = selectedKeywords.filter((keywordId) => keywordId !== id);
		return this.saveSelectedKeywords(updatedSelected);
	}

	/**
	 * Get sync queue from IndexedDB
	 */
	async getSyncQueue(): Promise<KeywordSyncAction[]> {
		await this.ensureMigration();
		return keywordIndexedDB.getSyncQueue();
	}

	/**
	 * Add action to sync queue
	 */
	async addToSyncQueue(action: Omit<KeywordSyncAction, 'timestamp'>): Promise<void> {
		await this.ensureMigration();
		const newAction: KeywordSyncAction = {
			...action,
			timestamp: Date.now(),
		};
		return keywordIndexedDB.addToSyncQueue(newAction);
	}

	/**
	 * Remove action from sync queue
	 */
	async removeFromSyncQueue(actionId: string): Promise<void> {
		await this.ensureMigration();
		return keywordIndexedDB.removeFromSyncQueue(actionId);
	}

	/**
	 * Clear sync queue
	 */
	async clearSyncQueue(): Promise<void> {
		await this.ensureMigration();
		return keywordIndexedDB.clearSyncQueue();
	}

	/**
	 * Clear all keyword data from IndexedDB
	 */
	async clearAll(): Promise<void> {
		await this.ensureMigration();
		return keywordIndexedDB.clearAllKeywordData();
	}

	/**
	 * Reset IndexedDB in case of corruption or version issues
	 */
	async resetIndexedDB(): Promise<void> {
		try {
			// Force recreate the database with all object stores
			await forceRecreateDatabase();

			// Reset migration flag to allow re-migration if needed
			if (typeof window !== 'undefined') {
				localStorage.removeItem('vocab-keywords-migration-completed');
			}

			// Reinitialize migration
			this.migrationPromise = this.migrateFromLocalStorage();
			await this.migrationPromise;

			console.log('IndexedDB reset completed successfully');
		} catch (error) {
			console.error('Failed to reset IndexedDB:', error);
			throw error;
		}
	}

	/**
	 * Generate unique ID for sync actions
	 */
	generateActionId(): string {
		return keywordIndexedDB.generateActionId();
	}
}

export const keywordStorage = KeywordStorage.getInstance();
